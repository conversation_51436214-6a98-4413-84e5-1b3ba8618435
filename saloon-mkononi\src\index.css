@import "tailwindcss";

@import url('https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@300;400;500;600;700&family=Nanum+Myeongjo:wght@400;700;800&family=Playfair+Display:ital,wght@0,400..900;1,400..900&family=Poppins:wght@300;400;500&display=swap');

@layer base {
  body {
    background-color: #111111;
    color: #FFFFFF;
    font-family: 'Poppins', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Make all heading tags use Cormorant font */
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Cormorant Garamond', serif;
  }
}

@layer components {
  .font-serif-display {
    font-family: 'Playfair Display', serif;
  }

  .font-nanum-myeongjo {
    font-family: 'Nanum Myeongjo', serif;
  }

  /* Custom letter spacing to match the design */
  .tracking-widest-plus {
    letter-spacing: 0.25em;
  }

  /* Custom styles for service numbers */
  .service-number {
    position: absolute;
    left: -1.5rem;
    bottom: -1.5rem;
    font-size: 5rem;
    line-height: 1;
    color: rgba(255, 255, 255, 0.15);
    z-index: -1;
  }

  /* Mobile responsive service numbers */
  @media (max-width: 640px) {
    .service-number {
      left: -0.75rem;
      bottom: -0.75rem;
      font-size: 3rem;
    }
  }

  @media (min-width: 641px) and (max-width: 768px) {
    .service-number {
      left: -1rem;
      bottom: -1rem;
      font-size: 4rem;
    }
  }

  /* Gallery styles */
  .gallery {
    position: relative;
    overflow: hidden;
  }

  .gallery-track {
    position: relative;
    width: 100%;
    will-change: transform;
  }

  .card {
    height: 400px;
    overflow: hidden;
  }

  .card-image-wrapper {
    height: 135%;
    will-change: transform;
  }

  .card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* Mobile-specific utilities */
  .mobile-text-balance {
    text-wrap: balance;
  }

  /* Improved touch targets for mobile */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Mobile-friendly spacing */
  .mobile-safe-area {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* Smooth scrolling for mobile */
  @media (max-width: 768px) {
    html {
      scroll-behavior: smooth;
      -webkit-overflow-scrolling: touch;
    }

    /* Prevent horizontal scroll on mobile */
    body {
      overflow-x: hidden;
    }

    /* Better button spacing on mobile */
    .mobile-button-spacing {
      margin: 0.5rem 0;
    }
  }
}
