const ExpertTeam = () => {
  return (
    <section className="py-12 sm:py-16 lg:py-20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-12 flex flex-col md:flex-row-reverse items-center gap-8 sm:gap-12 lg:gap-20">
        {/* Image */}
        <div className="w-full md:w-2/5">
          <img
            src="https://i.ibb.co/JqKkXqW/crop-5.png"
            alt="Two women posing together in black and white"
            className="w-full h-auto object-cover rounded-sm"
          />
        </div>

        {/* Content */}
        <div className="w-full md:w-3/5 md:pr-8 text-center md:text-left">
          <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl leading-tight md:leading-none">
            A Community<br />
            <span className="italic">of EXPERTS</span>
          </h2>
          <p className="mt-6 sm:mt-8 text-xs leading-relaxed font-light text-gray-400 max-w-md mx-auto md:mx-0">
            We partner with top-rated salons and independent stylists who are masters of their craft. Each partner is vetted for quality and professionalism.
          </p>
          <p className="mt-4 text-xs leading-relaxed font-light text-gray-400 max-w-md mx-auto md:mx-0">
            Explore portfolios, check ratings, and read authentic client reviews to find the perfect professional for your style and needs.
          </p>
          <div className="mt-10 sm:mt-12">
            <a href="#" className="inline-block border border-white/70 px-6 py-2.5 rounded-full hover:bg-white hover:text-black transition-colors duration-300 text-[10px] uppercase tracking-wider">
              Join as a Partner
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}

export default ExpertTeam
