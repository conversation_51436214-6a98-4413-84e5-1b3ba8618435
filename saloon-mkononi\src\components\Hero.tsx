const Hero = () => {
  return (
    <section className="flex flex-col md:flex-row mt-8 sm:mt-12 lg:mt-20">
      {/* Text Content */}
      <div className="w-full md:w-1/2 flex items-center justify-center px-4 py-8 sm:p-8 md:p-12 lg:p-24 xl:p-32">
        <div className="max-w-md text-left w-full">
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-5xl font-normal leading-tight sm:leading-snug">
            <span className="block">Beauty Services</span>
            <span className="italic">At Your Fingertips</span>
          </h1>
          <p className="mt-6 sm:mt-8 text-xs sm:text-[11px] leading-relaxed font-light text-gray-400">
            Discover and book appointments with the best salons and stylists in your city. <PERSON><PERSON> Mkononi makes finding your next look effortless.
          </p>
          <p className="mt-4 sm:mt-5 text-xs sm:text-[11px] leading-relaxed font-light text-gray-400">
            Browse services, read real reviews, and book your spot 24/7. Say goodbye to waiting and hello to convenience, all from the palm of your hand.
          </p>
          <div className="mt-8 sm:mt-12">
            <a href="#" className="inline-block border border-white/70 px-6 py-2.5 rounded-full hover:bg-white hover:text-black transition-colors duration-300 text-[10px] sm:text-[10px] uppercase tracking-wider">
              Explore Salons
            </a>
          </div>
        </div>
      </div>

      {/* Image */}
      <div className="w-full md:w-1/2 h-[50vh] sm:h-[60vh] md:h-auto min-h-[400px] md:min-h-[500px]">
        <img
          src="../public/photos/phoneOne.jpeg"
          alt="A woman with short dark hair looking over her shoulder"
          className="w-full h-full object-cover"
        />
      </div>
    </section>
  )
}

export default Hero
