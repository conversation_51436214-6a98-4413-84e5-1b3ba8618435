import { useState } from 'react'

const Header = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen)
  }

  return (
    <header className="container mx-auto px-4 sm:px-6 lg:px-12 py-4 sm:py-6 lg:py-8 relative">
      <nav className="flex justify-between items-center">
        {/* Left side - Logo for desktop */}
        <div className="hidden md:flex items-center space-x-10 text-[10px] uppercase tracking-wider">
          <a href="#" className="hover:opacity-80 transition-opacity">
            <img src="/photos/logo.png" alt="Saloon Mkononi Logo" className="h-6 lg:h-8 w-auto" />
          </a>
        </div>

        {/* Center - Brand name */}
        <div className="text-center absolute left-1/2 -translate-x-1/2">
          <div className="font-serif-display text-xl sm:text-2xl lg:text-2xl font-bold">SALOON</div>
          <div className="text-[8px] sm:text-[9px] tracking-widest-plus mt-1">MKONONI</div>
        </div>

        {/* Right side - Desktop navigation */}
        <div className="hidden md:flex items-center space-x-6 lg:space-x-10 text-[10px] uppercase tracking-wider">
          <a href="#" className="hover:text-gray-300 transition-colors">For Salons</a>
          <a href="#" className="hover:text-gray-300 transition-colors">Contact</a>
          <a href="#" className="border border-white/70 px-4 lg:px-5 py-2 rounded-full hover:bg-white hover:text-black transition-colors duration-300 text-[10px]">Get The App</a>
        </div>

        {/* Mobile Menu Button */}
        <div className="md:hidden">
          <button
            onClick={toggleMobileMenu}
            className="text-white focus:outline-none p-2 -mr-2"
            aria-label="Toggle mobile menu"
          >
            {isMobileMenuOpen ? (
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            ) : (
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16m-7 6h7" />
              </svg>
            )}
          </button>
        </div>
      </nav>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div className="md:hidden absolute top-full left-0 right-0 bg-[#111111] border-t border-gray-800/50 z-50">
          <div className="px-4 py-6 space-y-4">
            <a
              href="#"
              className="block text-sm uppercase tracking-wider hover:text-gray-300 transition-colors py-2"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              For Salons
            </a>
            <a
              href="#"
              className="block text-sm uppercase tracking-wider hover:text-gray-300 transition-colors py-2"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Contact
            </a>
            <a
              href="#"
              className="block border border-white/70 px-6 py-3 rounded-full hover:bg-white hover:text-black transition-colors duration-300 text-sm uppercase tracking-wider text-center mt-4"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Get The App
            </a>
          </div>
        </div>
      )}
    </header>
  )
}

export default Header
